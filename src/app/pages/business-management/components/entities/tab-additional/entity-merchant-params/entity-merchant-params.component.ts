import { Component, Input, OnInit, OnDestroy } from '@angular/core';
import { FormControl, FormGroup, FormBuilder } from '@angular/forms';
import { TranslateService } from '@ngx-translate/core';
import { SwHubAuthService, SwuiNotificationsService } from '@skywind-group/lib-swui';
import { finalize, map, switchMap, takeUntil } from 'rxjs/operators';

import { Entity, UpdateMerchantEntityData } from '../../../../../../common/models/entity.model';
import { EntityService } from '../../../../../../common/services/entity.service';
import { BehaviorSubject, Observable, Subject } from 'rxjs';
import { PERMISSIONS_LIST } from '../../../../../../app.constants';


@Component({
  selector: 'entity-merchant-params',
  templateUrl: './entity-merchant-params.component.html',
  styleUrls: ['./entity-merchant-params.component.scss']
})

export class EntityMerchantParamsComponent implements OnInit, OnDestroy {
  @Input('entity')
  set setWidgets( entity: Entity | undefined ) {
    this.path$.next(entity?.path);
  }

  readonly form: FormGroup;
  loading = false;
  public readonly allowedEdit: boolean;
  public readonly isSuperAdmin: boolean;

  private entity: Entity;

  private readonly path$ = new BehaviorSubject<string | undefined>(undefined);
  private readonly entity$: Observable<Entity>;
  private readonly destroyed$ = new Subject<void>();

  constructor( private readonly entityService: EntityService<Entity>,
               private readonly translate: TranslateService,
               private readonly notifications: SwuiNotificationsService,
               private readonly fb: FormBuilder,
               authService: SwHubAuthService,
  ) {
    this.allowedEdit = authService.allowedTo(PERMISSIONS_LIST.MERCHANT_EDIT);
    this.isSuperAdmin = authService.isSuperAdmin;

    const isReadonly = !this.isSuperAdmin;

    this.form = this.fb.group({
      sameUrlForTerminalLoginAndTicket: new FormControl({ value: '', disabled: isReadonly }),
      gsId: new FormControl({ value: '', disabled: isReadonly }),
      gpId: new FormControl({ value: '', disabled: isReadonly }),
      supportTransfer: new FormControl({ value: false, disabled: isReadonly }),
      supportForceFinishAndRevert: new FormControl({ value: false, disabled: isReadonly }),
      forceFinishAndRevertInSWWalletOnly: new FormControl({ value: false, disabled: isReadonly }),
      serverUrl: new FormControl({ value: '', disabled: isReadonly }),
      password: new FormControl({ value: '', disabled: isReadonly }),
      username: new FormControl({ value: '', disabled: isReadonly }),
      isUnderAAMSRegulation: new FormControl({ value: false, disabled: isReadonly }),
      isPromoInternal: new FormControl({ value: false, disabled: isReadonly }),
      walletPerGame: new FormControl({ value: false, disabled: isReadonly }),
      ignoreBalanceRequestError: new FormControl({ value: false, disabled: isReadonly }),
      supportPlayMoney: new FormControl({ value: false, disabled: isReadonly }),
      gameLogoutOptions: this.fb.group({
        type: new FormControl({ value: '', disabled: isReadonly }),
        maxRetryAttempts: new FormControl({ value: '', disabled: isReadonly }),
        maxSessionTimeout: new FormControl({ value: '', disabled: isReadonly })
      })
    });

    this.entity$ = this.path$.pipe(
      switchMap(path => this.entityService.getMerchantEntityItem(path)),
      map(entity => new Entity(entity))
    );
  }

  ngOnInit() {
    this.entity$.pipe(
      takeUntil(this.destroyed$)
    ).subscribe(entity => {
      this.entity = entity;
      this.form.patchValue(entity?.merchant?.params ?? {});
    });
  }

  ngOnDestroy() {
    this.destroyed$.next();
    this.destroyed$.complete();
  }

  onSubmit() {
    if (this.form.valid && !this.loading && this.isSuperAdmin) {
      const updateData: UpdateMerchantEntityData = { ...this.entity.asUpdateMerchantData() };
      const formValue = this.form.getRawValue();

      updateData.params = {
        ...updateData.params,
        sameUrlForTerminalLoginAndTicket: formValue.sameUrlForTerminalLoginAndTicket,
        gsId: formValue.gsId,
        gpId: formValue.gpId,
        supportTransfer: formValue.supportTransfer,
        supportForceFinishAndRevert: formValue.supportForceFinishAndRevert,
        forceFinishAndRevertInSWWalletOnly: formValue.forceFinishAndRevertInSWWalletOnly,
        serverUrl: formValue.serverUrl,
        password: formValue.password,
        username: formValue.username,
        isUnderAAMSRegulation: formValue.isUnderAAMSRegulation,
        isPromoInternal: formValue.isPromoInternal,
        walletPerGame: formValue.walletPerGame,
        ignoreBalanceRequestError: formValue.ignoreBalanceRequestError,
        supportPlayMoney: formValue.supportPlayMoney,
        gameLogoutOptions: formValue.gameLogoutOptions
      };

      this.loading = true;
      this.entityService.updateMerchantEntityItem(updateData, this.path$.value).pipe(
        finalize(() => this.loading = false),
        switchMap(() => this.translate.get('INTEGRATIONS.notificationConfigSaved'))
      ).subscribe(message => {
        this.notifications.success(message, '');
      });
    }
  }
}
