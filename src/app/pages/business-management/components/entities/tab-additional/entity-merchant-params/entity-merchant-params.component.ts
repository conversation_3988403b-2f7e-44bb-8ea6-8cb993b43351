import { Component, Input, OnInit, OnDestroy } from '@angular/core';
import { FormGroup, FormBuilder, Validators } from '@angular/forms';
import { TranslateService } from '@ngx-translate/core';
import { SwHubAuthService, SwuiNotificationsService, DynamicFormOptionData } from '@skywind-group/lib-swui';
import { distinctUntilChanged, filter, finalize, map, switchMap, takeUntil, startWith } from 'rxjs/operators';

import { Entity, UpdateMerchantEntityData } from '../../../../../../common/models/entity.model';
import { EntityService } from '../../../../../../common/services/entity.service';
import { MerchantTypesService, MerchantTypeSchema } from '../../../../../../common/services/merchant-types.service';
import { ValidationService } from '../../../../../../common/services/validation.service';
import { BehaviorSubject, Observable, Subject, combineLatest } from 'rxjs';
import { PERMISSIONS_LIST } from '../../../../../../app.constants';

const getDefaultSchema = (params: { [field: string]: any }): DynamicFormOptionData => {
  return {
    sameUrlForTerminalLoginAndTicket: {
      type: 'boolean',
      title: 'MERCHANT.PARAMETERS.sameUrlForTerminalLoginAndTicket',
      value: params.sameUrlForTerminalLoginAndTicket || false
    }
  };
};

function setDynamicFormValues(typeSchema: MerchantTypeSchema, params: { [field: string]: any }): DynamicFormOptionData | undefined {
  if (typeSchema && typeSchema.schema) {
    const schema = { ...typeSchema.schema };
    Object.keys(schema).forEach(key => {
      const option = schema[key];
      option.value = params[key];
    });

    if ('serverUrl' in schema) {
      schema.serverUrl['validation'] = {
        validators: [Validators.required, ValidationService.fullUrlValidation]
      };
    }

    return schema;
  }
  return undefined;
}

@Component({
  selector: 'entity-merchant-params',
  templateUrl: './entity-merchant-params.component.html',
  styleUrls: ['./entity-merchant-params.component.scss']
})

export class EntityMerchantParamsComponent implements OnInit, OnDestroy {
  @Input('entity')
  set setWidgets(entity: Entity | undefined) {
    this.path$.next(entity?.path);
  }

  readonly form: FormGroup;
  loading = false;
  public readonly allowedEdit: boolean;
  public readonly isSuperAdmin: boolean;

  merchantParamsSchema: DynamicFormOptionData = getDefaultSchema({});

  private entity: Entity;

  private readonly path$ = new BehaviorSubject<string | undefined>(undefined);
  private readonly entity$: Observable<Entity>;
  private readonly typeSchema$: Observable<MerchantTypeSchema | null>;
  private readonly destroyed$ = new Subject<void>();

  constructor(private readonly entityService: EntityService<Entity>,
    private readonly translate: TranslateService,
    private readonly notifications: SwuiNotificationsService,
    private readonly fb: FormBuilder,
    private readonly merchantTypesService: MerchantTypesService,
    authService: SwHubAuthService,
  ) {
    this.allowedEdit = authService.allowedTo(PERMISSIONS_LIST.MERCHANT_EDIT);
    this.isSuperAdmin = authService.isSuperAdmin;

    // Create an empty form group that will be populated dynamically
    this.form = this.fb.group({});

    this.entity$ = this.path$.pipe(
      switchMap(path => this.entityService.getMerchantEntityItem(path)),
      map(entity => new Entity(entity))
    );

    this.typeSchema$ = this.entity$.pipe(
      filter(entity => !!entity),
      map(entity => ({ type: entity?.merchant?.type, path: entity?.path })),
      distinctUntilChanged((prev, curr) => prev.type === curr.type && prev.path === curr.path),
      filter(({ type, path }) => !!type && !!path),
      switchMap(({ type, path }) => {
        return this.merchantTypesService.get(type, path);
      }),
      startWith(null as MerchantTypeSchema | null)
    );
  }

  ngOnInit() {
    combineLatest([this.entity$, this.typeSchema$]).pipe(
      takeUntil(this.destroyed$)
    ).subscribe(([entity, typeSchema]) => {
      this.entity = entity;

      if (typeSchema?.schema) {
        this.merchantParamsSchema = setDynamicFormValues(typeSchema, entity?.merchant?.params || {});
      } else {
        // Use default schema with sameUrlForTerminalLoginAndTicket field
        this.merchantParamsSchema = getDefaultSchema(entity?.merchant?.params || {});
      }
    });
  }

  ngOnDestroy() {
    this.destroyed$.next();
    this.destroyed$.complete();
  }

  onSubmit() {
    if (this.form.valid && !this.loading && this.isSuperAdmin) {
      const updateData: UpdateMerchantEntityData = { ...this.entity.asUpdateMerchantData() };
      const formValue = this.form.getRawValue();

      // Update params with all form values from dynamic form
      updateData.params = {
        ...updateData.params,
        ...formValue
      };

      this.loading = true;
      this.entityService.updateMerchantEntityItem(updateData, this.path$.value).pipe(
        finalize(() => this.loading = false),
        switchMap(() => this.translate.get('INTEGRATIONS.notificationConfigSaved'))
      ).subscribe(message => {
        this.notifications.success(message, '');
      });
    }
  }
}
