<div class="entity-merchant-params">
  <div *ngIf="merchantParamsSchema" [formGroup]="form">
    <lib-dynamic-form
      [options]="merchantParamsSchema"
      [form]="form"
      [readonly]="!isSuperAdmin"
      [submitted]="loading">
    </lib-dynamic-form>
  </div>

  <div *ngIf="!merchantParamsSchema" class="entity-merchant-params__empty">
    <p>{{'MERCHANT.PARAMETERS.noSchemaAvailable' | translate}}</p>
  </div>

  <div class="entity-merchant-params__footer" *ngIf="isSuperAdmin && merchantParamsSchema">
    <button mat-stroked-button color="primary" [disabled]="loading" (click)="onSubmit()">
      <i *ngIf="loading" class="icon-spinner4 spinner"></i>
      {{'ALL.save' | translate}}
    </button>
  </div>
</div>
