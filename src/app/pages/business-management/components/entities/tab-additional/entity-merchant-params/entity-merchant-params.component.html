<div [formGroup]="form" class="entity-merchant-params">
  <div class="entity-merchant-params__row">
    <mat-form-field appearance="outline">
      <mat-label>{{'MERCHANT.PARAMETERS.gsId' | translate}}</mat-label>
      <input matInput formControlName="gsId">
    </mat-form-field>

    <mat-form-field appearance="outline">
      <mat-label>{{'MERCHANT.PARAMETERS.gpId' | translate}}</mat-label>
      <input matInput formControlName="gpId">
    </mat-form-field>
  </div>

  <div class="entity-merchant-params__row">
    <mat-form-field appearance="outline">
      <mat-label>{{'MERCHANT.PARAMETERS.serverUrl' | translate}}</mat-label>
      <input matInput formControlName="serverUrl">
    </mat-form-field>

    <mat-form-field appearance="outline">
      <mat-label>{{'MERCHANT.PARAMETERS.username' | translate}}</mat-label>
      <input matInput formControlName="username">
    </mat-form-field>
  </div>

  <div class="entity-merchant-params__row">
    <mat-form-field appearance="outline">
      <mat-label>{{'MERCHANT.PARAMETERS.password' | translate}}</mat-label>
      <input matInput type="password" formControlName="password">
    </mat-form-field>
  </div>

  <div class="entity-merchant-params__row">
    <mat-slide-toggle formControlName="sameUrlForTerminalLoginAndTicket">
      {{'MERCHANT.PARAMETERS.sameUrlForTerminalLoginAndTicket' | translate}}
    </mat-slide-toggle>
  </div>

  <div class="entity-merchant-params__row">
    <mat-slide-toggle formControlName="supportTransfer">
      {{'MERCHANT.PARAMETERS.supportTransfer' | translate}}
    </mat-slide-toggle>
  </div>

  <div class="entity-merchant-params__row">
    <mat-slide-toggle formControlName="supportForceFinishAndRevert">
      {{'MERCHANT.PARAMETERS.supportForceFinishAndRevert' | translate}}
    </mat-slide-toggle>
  </div>

  <div class="entity-merchant-params__row">
    <mat-slide-toggle formControlName="forceFinishAndRevertInSWWalletOnly">
      {{'MERCHANT.PARAMETERS.forceFinishAndRevertInSWWalletOnly' | translate}}
    </mat-slide-toggle>
  </div>

  <div class="entity-merchant-params__row">
    <mat-slide-toggle formControlName="isUnderAAMSRegulation">
      {{'MERCHANT.PARAMETERS.isUnderAAMSRegulation' | translate}}
    </mat-slide-toggle>
  </div>

  <div class="entity-merchant-params__row">
    <mat-slide-toggle formControlName="isPromoInternal">
      {{'MERCHANT.PARAMETERS.isPromoInternal' | translate}}
    </mat-slide-toggle>
  </div>

  <div class="entity-merchant-params__row">
    <mat-slide-toggle formControlName="walletPerGame">
      {{'MERCHANT.PARAMETERS.walletPerGame' | translate}}
    </mat-slide-toggle>
  </div>

  <div class="entity-merchant-params__row">
    <mat-slide-toggle formControlName="ignoreBalanceRequestError">
      {{'MERCHANT.PARAMETERS.ignoreBalanceRequestError' | translate}}
    </mat-slide-toggle>
  </div>

  <div class="entity-merchant-params__row">
    <mat-slide-toggle formControlName="supportPlayMoney">
      {{'MERCHANT.PARAMETERS.supportPlayMoney' | translate}}
    </mat-slide-toggle>
  </div>

  <div formGroupName="gameLogoutOptions" class="entity-merchant-params__section">
    <h3>{{'MERCHANT.PARAMETERS.gameLogoutOptions.common' | translate}}</h3>

    <div class="entity-merchant-params__row">
      <mat-form-field appearance="outline">
        <mat-label>{{'MERCHANT.PARAMETERS.gameLogoutOptions.type' | translate}}</mat-label>
        <mat-select formControlName="type">
          <mat-option value="unfinished">{{'MERCHANT.PARAMETERS.gameLogoutOptions.unfinished' | translate}}</mat-option>
          <mat-option value="all">{{'MERCHANT.PARAMETERS.gameLogoutOptions.all' | translate}}</mat-option>
        </mat-select>
      </mat-form-field>

      <mat-form-field appearance="outline">
        <mat-label>{{'MERCHANT.PARAMETERS.gameLogoutOptions.maxRetryAttempts' | translate}}</mat-label>
        <input matInput type="number" formControlName="maxRetryAttempts">
      </mat-form-field>
    </div>

    <div class="entity-merchant-params__row">
      <mat-form-field appearance="outline">
        <mat-label>{{'MERCHANT.PARAMETERS.gameLogoutOptions.maxSessionTimeout' | translate}}</mat-label>
        <input matInput type="number" formControlName="maxSessionTimeout">
      </mat-form-field>
    </div>
  </div>

  <div class="entity-merchant-params__footer" *ngIf="isSuperAdmin">
    <button mat-stroked-button color="primary" [disabled]="loading" (click)="onSubmit()">
      <i *ngIf="loading" class="icon-spinner4 spinner"></i>
      {{'ALL.save' | translate}}
    </button>
  </div>
</div>
